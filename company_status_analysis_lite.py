#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业历年经营状态分析脚本（轻量版）
功能：分析2010-2024年每年的正常经营企业数量，内存优化版本
"""

import pandas as pd
import os
import glob
from datetime import datetime
from pathlib import Path
import gc

def extract_company_events(data_dir):
    """
    提取企业事件数据（成立、注销、吊销）和2024年正常企业
    
    Args:
        data_dir: 数据目录路径
    
    Returns:
        tuple: (2024年正常企业集合, 各年事件字典)
    """
    print(f"开始提取企业事件数据: {data_dir}")
    
    parquet_files = glob.glob(os.path.join(data_dir, "*.parquet"))
    print(f"找到 {len(parquet_files)} 个parquet文件")
    
    # 存储结果
    normal_2024 = set()
    yearly_events = {
        'established': {},  # {年份: 企业ID集合}
        'cancelled': {},    # {年份: 企业ID集合}
        'revoked': {}       # {年份: 企业ID集合}
    }
    
    total_processed = 0
    
    for i, file_path in enumerate(parquet_files, 1):
        print(f"处理文件 {i}/{len(parquet_files)}: {os.path.basename(file_path)}")
        
        try:
            df = pd.read_parquet(file_path)
            
            # 过滤掉三个日期字段全部为空的记录
            date_columns = ['establish_date', 'cancel_date', 'revoke_date']
            valid_mask = ~(df[date_columns].isna().all(axis=1))
            df = df[valid_mask].copy()
            
            if len(df) == 0:
                continue
            
            # 提取2024年正常企业
            normal_companies = df[df['company_status_clean'] == 1]['lc_company_id'].unique()
            normal_2024.update(normal_companies)
            
            # 提取年份
            df['establish_year'] = df['establish_date'].dt.year
            df['cancel_year'] = df['cancel_date'].dt.year
            df['revoke_year'] = df['revoke_date'].dt.year
            
            # 按年份分组提取事件
            for year in range(2010, 2025):
                # 成立事件
                established = df[df['establish_year'] == year]['lc_company_id'].unique()
                if len(established) > 0:
                    if year not in yearly_events['established']:
                        yearly_events['established'][year] = set()
                    yearly_events['established'][year].update(established)
                
                # 注销事件
                cancelled = df[df['cancel_year'] == year]['lc_company_id'].unique()
                if len(cancelled) > 0:
                    if year not in yearly_events['cancelled']:
                        yearly_events['cancelled'][year] = set()
                    yearly_events['cancelled'][year].update(cancelled)
                
                # 吊销事件
                revoked = df[df['revoke_year'] == year]['lc_company_id'].unique()
                if len(revoked) > 0:
                    if year not in yearly_events['revoked']:
                        yearly_events['revoked'][year] = set()
                    yearly_events['revoked'][year].update(revoked)
            
            total_processed += len(df)
            
            # 清理内存
            del df
            if i % 20 == 0:
                gc.collect()
                print(f"已处理 {total_processed:,} 条有效记录")
                
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue
    
    print(f"数据提取完成，总处理记录: {total_processed:,}")
    print(f"2024年正常企业数量: {len(normal_2024):,}")
    
    return normal_2024, yearly_events

def calculate_yearly_companies(normal_2024, yearly_events):
    """
    基于事件数据计算各年度正常企业
    
    Args:
        normal_2024: 2024年正常企业集合
        yearly_events: 各年事件字典
    
    Returns:
        dict: {年份: 企业ID集合}
    """
    print("开始计算各年度正常企业...")
    
    yearly_normal = {2024: normal_2024.copy()}
    current_normal = normal_2024.copy()
    
    for year in range(2023, 2009, -1):  # 从2023年到2010年
        print(f"计算 {year} 年...")
        
        next_year = year + 1
        
        # 获取下一年的事件
        established = yearly_events['established'].get(next_year, set())
        cancelled = yearly_events['cancelled'].get(next_year, set())
        revoked = yearly_events['revoked'].get(next_year, set())
        
        # 计算公式：某年正常企业 = 下一年正常企业 - 下一年新成立 + 下一年注销 + 下一年吊销
        # 使用集合操作：减法用 - ，加法用 | (并集)
        current_normal = current_normal - established | cancelled | revoked
        yearly_normal[year] = current_normal.copy()
        
        print(f"  {year}年正常企业: {len(current_normal):,}")
        print(f"  移除{next_year}年新成立: {len(established):,}")
        print(f"  加回{next_year}年注销: {len(cancelled):,}")
        print(f"  加回{next_year}年吊销: {len(revoked):,}")
    
    return yearly_normal

def save_results_lite(yearly_normal, output_dir):
    """保存结果（轻量版）"""
    print(f"保存结果到: {output_dir}")
    Path(output_dir).mkdir(exist_ok=True)
    
    # 保存各年度企业ID列表
    for year in sorted(yearly_normal.keys()):
        company_ids = yearly_normal[year]
        
        # 保存为文本文件
        txt_file = os.path.join(output_dir, f"normal_companies_{year}.txt")
        with open(txt_file, 'w', encoding='utf-8') as f:
            for company_id in sorted(company_ids):
                f.write(f"{company_id}\n")
        
        print(f"{year}年: {len(company_ids):,} 个企业 -> {txt_file}")
    
    # 保存汇总
    summary_file = os.path.join(output_dir, "yearly_summary.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("企业历年经营状态分析汇总\n")
        f.write("=" * 50 + "\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for year in sorted(yearly_normal.keys()):
            count = len(yearly_normal[year])
            f.write(f"{year}年: {count:,} 个正常企业\n")
    
    print(f"汇总统计: {summary_file}")

def main():
    """主函数"""
    data_dir = "processed_company_data_20250729_175614"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"sz_company_status_analysis_{timestamp}"
    
    print("=" * 60)
    print("企业历年经营状态分析（轻量版）")
    print("=" * 60)
    
    try:
        if not os.path.exists(data_dir):
            raise ValueError(f"数据目录不存在: {data_dir}")
        
        # 1. 提取企业事件数据
        normal_2024, yearly_events = extract_company_events(data_dir)
        
        # 2. 计算各年度正常企业
        yearly_normal = calculate_yearly_companies(normal_2024, yearly_events)
        
        # 3. 保存结果
        save_results_lite(yearly_normal, output_dir)
        
        print("=" * 60)
        print("分析完成！")
        print(f"结果目录: {output_dir}")
        print("=" * 60)
        
    except Exception as e:
        print(f"错误: {e}")
        raise

if __name__ == "__main__":
    main()
