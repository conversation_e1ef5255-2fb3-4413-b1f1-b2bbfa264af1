#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业行业分布数据可视化脚本
功能：生成第二产业L2层级的叠加柱状图
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from datetime import datetime
import os

def setup_chinese_font():
    """
    设置中文字体
    """
    # 尝试设置中文字体
    chinese_fonts = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',          # 宋体
        'KaiTi',           # 楷体
        'FangSong'         # 仿宋
    ]
    
    for font_name in chinese_fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            print(f"成功设置中文字体: {font_name}")
            return font_name
        except:
            continue
    
    print("警告: 未找到合适的中文字体，可能影响中文显示")
    return None

def load_data(excel_file, sheet_name):
    """
    从Excel文件加载数据
    
    Args:
        excel_file: Excel文件路径
        sheet_name: sheet页名称
    
    Returns:
        DataFrame: 加载的数据
    """
    print(f"加载数据: {excel_file} - {sheet_name}")
    
    try:
        df = pd.read_excel(excel_file, sheet_name=sheet_name)
        print(f"数据加载成功: {len(df)} 行数据")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def prepare_data(df):
    """
    准备可视化数据
    
    Args:
        df: 原始数据DataFrame
    
    Returns:
        DataFrame: 处理后的数据
    """
    print("准备可视化数据...")
    
    # 检查必要的列是否存在
    required_columns = ['行业名称', '2010年占比(%)', '2020年占比(%)', '2024年占比(%)']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"缺少必要的列: {missing_columns}")
        return None
    
    # 复制数据并处理
    data = df[required_columns].copy()
    
    # 处理缺失值
    data = data.fillna(0)
    
    # 按2024年占比降序排序
    data = data.sort_values('2024年占比(%)', ascending=False)
    
    # 过滤掉占比为0的行业（可选）
    data = data[data['2024年占比(%)'] > 0]
    
    print(f"处理后数据: {len(data)} 个行业")
    print(f"2024年占比范围: {data['2024年占比(%)'].min():.2f}% - {data['2024年占比(%)'].max():.2f}%")
    
    return data

def create_stacked_bar_chart(data, output_file):
    """
    创建横向堆叠柱状图

    Args:
        data: 处理后的数据
        output_file: 输出文件路径
    """
    print("创建横向堆叠柱状图...")

    # 设置图表尺寸
    fig_width = 16
    fig_height = max(12, len(data) * 0.4)  # 根据行业数量动态调整高度

    fig, ax = plt.subplots(figsize=(fig_width, fig_height), dpi=300)

    # 准备数据
    industries = data['行业名称'].tolist()
    year_2010 = np.array(data['2010年占比(%)'].tolist())
    year_2020 = np.array(data['2020年占比(%)'].tolist())
    year_2024 = np.array(data['2024年占比(%)'].tolist())

    # 设置柱状图位置
    y = np.arange(len(industries))
    height = 0.6  # 柱状图高度

    # 使用更好的颜色方案（从左到右：2010年、2020年、2024年）
    colors = ['#3498db', '#e74c3c', '#2ecc71']  # 蓝色、红色、绿色

    # 创建横向堆叠柱状图
    # 左侧：2010年数据
    bars1 = ax.barh(y, year_2010, height, label='2010年',
                    color=colors[0], alpha=0.8, edgecolor='white', linewidth=0.5)

    # 中间：2020年数据（堆叠在2010年右侧）
    bars2 = ax.barh(y, year_2020, height, left=year_2010, label='2020年',
                    color=colors[1], alpha=0.8, edgecolor='white', linewidth=0.5)

    # 右侧：2024年数据（堆叠在2010年+2020年右侧）
    bars3 = ax.barh(y, year_2024, height, left=year_2010 + year_2020, label='2024年',
                    color=colors[2], alpha=0.8, edgecolor='white', linewidth=0.5)

    # 设置图表标题和标签
    ax.set_title('第二产业L2层级行业分布占比变化趋势\n(横向堆叠：2010年+2020年+2024年)',
                fontsize=18, fontweight='bold', pad=25)
    ax.set_ylabel('行业名称', fontsize=14, fontweight='bold')
    ax.set_xlabel('累计占比 (%)', fontsize=14, fontweight='bold')

    # 设置Y轴标签
    ax.set_yticks(y)
    ax.set_yticklabels(industries, fontsize=9)

    # 设置X轴
    max_total = np.max(year_2010 + year_2020 + year_2024)
    ax.set_xlim(0, max_total * 1.1)
    ax.grid(True, axis='x', alpha=0.3, linestyle='--', color='gray')

    # 美化轴刻度
    ax.tick_params(axis='x', labelsize=11)
    ax.tick_params(axis='y', labelsize=9)

    # 添加图例
    ax.legend(loc='lower right', fontsize=12, framealpha=0.95,
             fancybox=True, shadow=True)

    # 在堆叠柱状图上添加数值标签
    for i in range(len(industries)):
        # 2010年标签（左侧）
        if year_2010[i] > 0.5:  # 只显示占比大于0.5%的值
            ax.text(year_2010[i]/2, y[i], f'{year_2010[i]:.1f}%',
                   ha='center', va='center', fontsize=8, fontweight='bold', color='white')

        # 2020年标签（中间）
        if year_2020[i] > 0.5:
            ax.text(year_2010[i] + year_2020[i]/2, y[i], f'{year_2020[i]:.1f}%',
                   ha='center', va='center', fontsize=8, fontweight='bold', color='white')

        # 2024年标签（右侧）
        if year_2024[i] > 0.5:
            ax.text(year_2010[i] + year_2020[i] + year_2024[i]/2, y[i], f'{year_2024[i]:.1f}%',
                   ha='center', va='center', fontsize=8, fontweight='bold', color='white')

    # 添加背景色
    ax.set_facecolor('#f8f9fa')

    # 反转Y轴，使最大的行业显示在顶部
    ax.invert_yaxis()

    # 调整布局
    plt.tight_layout()

    # 保存图表
    plt.savefig(output_file, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"图表已保存: {output_file}")

    # 同时保存PDF版本
    pdf_file = output_file.replace('.png', '.pdf')
    plt.savefig(pdf_file, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"PDF版本已保存: {pdf_file}")

    plt.close()

def create_top_industries_chart(data, output_file, top_n=15):
    """
    创建前N个行业的横向堆叠图表

    Args:
        data: 处理后的数据
        output_file: 输出文件路径
        top_n: 显示前N个行业
    """
    print(f"创建前{top_n}个行业横向堆叠图表...")

    # 取前N个行业
    top_data = data.head(top_n)

    fig, ax = plt.subplots(figsize=(14, 10), dpi=300)

    # 准备数据
    industries = top_data['行业名称'].tolist()
    year_2010 = np.array(top_data['2010年占比(%)'].tolist())
    year_2020 = np.array(top_data['2020年占比(%)'].tolist())
    year_2024 = np.array(top_data['2024年占比(%)'].tolist())

    # 设置柱状图位置
    y = np.arange(len(industries))
    height = 0.6

    # 使用渐变色
    colors = ['#74b9ff', '#fd79a8', '#00b894']

    # 创建横向堆叠柱状图
    # 左侧：2010年数据
    bars1 = ax.barh(y, year_2010, height, label='2010年',
                    color=colors[0], alpha=0.9, edgecolor='white', linewidth=1)

    # 中间：2020年数据（堆叠在2010年右侧）
    bars2 = ax.barh(y, year_2020, height, left=year_2010, label='2020年',
                    color=colors[1], alpha=0.9, edgecolor='white', linewidth=1)

    # 右侧：2024年数据（堆叠在2010年+2020年右侧）
    bars3 = ax.barh(y, year_2024, height, left=year_2010 + year_2020, label='2024年',
                    color=colors[2], alpha=0.9, edgecolor='white', linewidth=1)

    # 设置图表标题和标签
    ax.set_title(f'第二产业L2层级前{top_n}个行业占比变化趋势\n(横向堆叠：2010年+2020年+2024年)',
                fontsize=16, fontweight='bold', pad=20)
    ax.set_ylabel('行业名称', fontsize=12, fontweight='bold')
    ax.set_xlabel('累计占比 (%)', fontsize=12, fontweight='bold')

    # 设置Y轴标签
    ax.set_yticks(y)
    ax.set_yticklabels(industries, fontsize=10)

    # 设置X轴
    max_total = np.max(year_2010 + year_2020 + year_2024)
    ax.set_xlim(0, max_total * 1.1)
    ax.grid(True, axis='x', alpha=0.3, linestyle='--')

    # 添加图例
    ax.legend(loc='lower right', fontsize=11, framealpha=0.9)

    # 在堆叠柱状图上添加数值标签
    for i in range(len(industries)):
        # 2010年标签（左侧）
        if year_2010[i] > 1.0:  # 只显示占比大于1%的值
            ax.text(year_2010[i]/2, y[i], f'{year_2010[i]:.1f}%',
                   ha='center', va='center', fontsize=9, fontweight='bold', color='white')

        # 2020年标签（中间）
        if year_2020[i] > 1.0:
            ax.text(year_2010[i] + year_2020[i]/2, y[i], f'{year_2020[i]:.1f}%',
                   ha='center', va='center', fontsize=9, fontweight='bold', color='white')

        # 2024年标签（右侧）
        if year_2024[i] > 1.0:
            ax.text(year_2010[i] + year_2020[i] + year_2024[i]/2, y[i], f'{year_2024[i]:.1f}%',
                   ha='center', va='center', fontsize=9, fontweight='bold', color='white')

    # 反转Y轴，使最大的行业显示在顶部
    ax.invert_yaxis()

    plt.tight_layout()

    # 保存图表
    plt.savefig(output_file, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"前{top_n}行业图表已保存: {output_file}")

    plt.close()

def main():
    """主函数"""
    # 输入文件
    excel_file = "sz_industry_analysis_by_levels_20250729.xlsx"
    sheet_name = "二产L2"

    # 输出文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file_all = f"secondary_industry_l2_all_chart_{timestamp}.png"
    output_file_top = f"secondary_industry_l2_top15_chart_{timestamp}.png"

    print("=" * 60)
    print("企业行业分布数据可视化")
    print("=" * 60)

    try:
        # 1. 设置中文字体
        setup_chinese_font()

        # 2. 检查输入文件
        if not os.path.exists(excel_file):
            raise FileNotFoundError(f"Excel文件不存在: {excel_file}")

        # 3. 加载数据
        df = load_data(excel_file, sheet_name)
        if df is None:
            raise ValueError("数据加载失败")

        # 4. 准备数据
        data = prepare_data(df)
        if data is None or len(data) == 0:
            raise ValueError("数据处理失败或无有效数据")

        # 5. 创建完整图表
        print("\n创建完整行业图表...")
        create_stacked_bar_chart(data, output_file_all)

        # 6. 创建前15个行业图表
        print("\n创建前15个行业图表...")
        create_top_industries_chart(data, output_file_top, top_n=15)

        print("=" * 60)
        print("可视化完成！")
        print(f"完整图表: {output_file_all}")
        print(f"前15行业图表: {output_file_top}")
        print(f"处理行业数量: {len(data)}")
        print("=" * 60)

        # 显示数据概览
        print("\n数据概览（前10个行业）:")
        print(data.head(10)[['行业名称', '2024年占比(%)']].to_string(index=False))

        # 显示增长趋势分析
        print("\n增长趋势分析（前5个行业）:")
        for i, row in data.head(5).iterrows():
            name = row['行业名称']
            ratio_2010 = row['2010年占比(%)']
            ratio_2020 = row['2020年占比(%)']
            ratio_2024 = row['2024年占比(%)']

            growth_2010_2020 = ratio_2020 - ratio_2010
            growth_2020_2024 = ratio_2024 - ratio_2020

            print(f"  {name}:")
            print(f"    2010-2020年变化: {growth_2010_2020:+.2f}%")
            print(f"    2020-2024年变化: {growth_2020_2024:+.2f}%")

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
