#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业行业分布数据可视化脚本
功能：生成第二产业L2层级的叠加柱状图
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from datetime import datetime
import os

def setup_chinese_font():
    """
    设置中文字体
    """
    # 尝试设置中文字体
    chinese_fonts = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',          # 宋体
        'KaiTi',           # 楷体
        'FangSong'         # 仿宋
    ]
    
    for font_name in chinese_fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            print(f"成功设置中文字体: {font_name}")
            return font_name
        except:
            continue
    
    print("警告: 未找到合适的中文字体，可能影响中文显示")
    return None

def load_data(excel_file, sheet_name):
    """
    从Excel文件加载数据
    
    Args:
        excel_file: Excel文件路径
        sheet_name: sheet页名称
    
    Returns:
        DataFrame: 加载的数据
    """
    print(f"加载数据: {excel_file} - {sheet_name}")
    
    try:
        df = pd.read_excel(excel_file, sheet_name=sheet_name)
        print(f"数据加载成功: {len(df)} 行数据")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def prepare_data(df):
    """
    准备可视化数据
    
    Args:
        df: 原始数据DataFrame
    
    Returns:
        DataFrame: 处理后的数据
    """
    print("准备可视化数据...")
    
    # 检查必要的列是否存在
    required_columns = ['行业名称', '2010年占比(%)', '2020年占比(%)', '2024年占比(%)']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"缺少必要的列: {missing_columns}")
        return None
    
    # 复制数据并处理
    data = df[required_columns].copy()
    
    # 处理缺失值
    data = data.fillna(0)
    
    # 按2024年占比降序排序
    data = data.sort_values('2024年占比(%)', ascending=False)
    
    # 过滤掉占比为0的行业（可选）
    data = data[data['2024年占比(%)'] > 0]
    
    print(f"处理后数据: {len(data)} 个行业")
    print(f"2024年占比范围: {data['2024年占比(%)'].min():.2f}% - {data['2024年占比(%)'].max():.2f}%")
    
    return data

def create_stacked_bar_chart(data, output_file):
    """
    创建三个独立的横向柱状图（水平排列）

    Args:
        data: 处理后的数据
        output_file: 输出文件路径
    """
    print("创建三个独立的横向柱状图（水平排列）...")

    # 设置图表尺寸
    fig_width = max(24, len(data) * 0.8)  # 根据行业数量动态调整宽度
    fig_height = max(12, len(data) * 0.3)  # 根据行业数量动态调整高度

    # 创建3个子图，水平排列
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(fig_width, fig_height), dpi=300)

    # 准备数据
    industries = data['行业名称'].tolist()
    year_2010_ratio = np.array(data['2010年占比(%)'].tolist())
    year_2020_ratio = np.array(data['2020年占比(%)'].tolist())
    year_2024_ratio = np.array(data['2024年占比(%)'].tolist())

    # 企业数量数据（检查列名是否存在）
    if '2010年企业数' in data.columns:
        year_2010_count = np.array(data['2010年企业数'].tolist())
        year_2020_count = np.array(data['2020年企业数'].tolist())
        year_2024_count = np.array(data['2024年企业数'].tolist())
    else:
        # 如果没有企业数量列，使用占比数据作为替代显示
        year_2010_count = year_2010_ratio
        year_2020_count = year_2020_ratio
        year_2024_count = year_2024_ratio

    # 设置柱状图位置
    y = np.arange(len(industries))
    height = 0.7

    # 使用统一的颜色方案
    bar_color = '#3498db'  # 统一使用蓝色

    # 设置X轴范围（所有子图使用相同范围）
    max_value = max(np.max(year_2010_ratio), np.max(year_2020_ratio), np.max(year_2024_ratio))
    xlim_max = max_value * 1.2

    # 创建Y轴标签（行业名称+占比）
    y_labels_2010 = [f"{name}({ratio:.1f}%)" for name, ratio in zip(industries, year_2010_ratio)]
    y_labels_2020 = [f"{name}({ratio:.1f}%)" for name, ratio in zip(industries, year_2020_ratio)]
    y_labels_2024 = [f"{name}({ratio:.1f}%)" for name, ratio in zip(industries, year_2024_ratio)]

    # 第一个子图：2010年数据
    ax1.barh(y, year_2010_ratio, height, color=bar_color, alpha=0.8, edgecolor='white', linewidth=0.5)
    ax1.set_title('2010年', fontsize=16, fontweight='bold', pad=20)
    ax1.set_xlim(0, xlim_max)
    ax1.grid(True, axis='x', alpha=0.3, linestyle='--', color='gray')
    ax1.set_xlabel('占比 (%)', fontsize=12, fontweight='bold')
    ax1.set_yticks(y)
    ax1.set_yticklabels(y_labels_2010, fontsize=9)

    # 添加数据标签
    for i, count in enumerate(year_2010_count):
        if year_2010_ratio[i] > 0.5:  # 只显示占比大于0.5%的值
            if '2010年企业数' in data.columns:
                ax1.text(year_2010_ratio[i] + xlim_max * 0.01, y[i], f'{count:,}家',
                        ha='left', va='center', fontsize=8, fontweight='bold')
            else:
                ax1.text(year_2010_ratio[i] + xlim_max * 0.01, y[i], f'{count:.1f}%',
                        ha='left', va='center', fontsize=8, fontweight='bold')

    # 第二个子图：2020年数据
    ax2.barh(y, year_2020_ratio, height, color=bar_color, alpha=0.8, edgecolor='white', linewidth=0.5)
    ax2.set_title('2020年', fontsize=16, fontweight='bold', pad=20)
    ax2.set_xlim(0, xlim_max)
    ax2.grid(True, axis='x', alpha=0.3, linestyle='--', color='gray')
    ax2.set_xlabel('占比 (%)', fontsize=12, fontweight='bold')
    ax2.set_yticks(y)
    ax2.set_yticklabels(y_labels_2020, fontsize=9)

    # 添加数据标签
    for i, count in enumerate(year_2020_count):
        if year_2020_ratio[i] > 0.5:
            if '2020年企业数' in data.columns:
                ax2.text(year_2020_ratio[i] + xlim_max * 0.01, y[i], f'{count:,}家',
                        ha='left', va='center', fontsize=8, fontweight='bold')
            else:
                ax2.text(year_2020_ratio[i] + xlim_max * 0.01, y[i], f'{count:.1f}%',
                        ha='left', va='center', fontsize=8, fontweight='bold')

    # 第三个子图：2024年数据
    ax3.barh(y, year_2024_ratio, height, color=bar_color, alpha=0.8, edgecolor='white', linewidth=0.5)
    ax3.set_title('2024年', fontsize=16, fontweight='bold', pad=20)
    ax3.set_xlim(0, xlim_max)
    ax3.grid(True, axis='x', alpha=0.3, linestyle='--', color='gray')
    ax3.set_xlabel('占比 (%)', fontsize=12, fontweight='bold')
    ax3.set_yticks(y)
    ax3.set_yticklabels(y_labels_2024, fontsize=9)

    # 添加数据标签
    for i, count in enumerate(year_2024_count):
        if year_2024_ratio[i] > 0.5:
            if '2024年企业数' in data.columns:
                ax3.text(year_2024_ratio[i] + xlim_max * 0.01, y[i], f'{count:,}家',
                        ha='left', va='center', fontsize=8, fontweight='bold')
            else:
                ax3.text(year_2024_ratio[i] + xlim_max * 0.01, y[i], f'{count:.1f}%',
                        ha='left', va='center', fontsize=8, fontweight='bold')

    # 反转Y轴，使最大的行业显示在顶部
    for ax in [ax1, ax2, ax3]:
        ax.invert_yaxis()
        ax.tick_params(axis='x', labelsize=10)
        ax.set_facecolor('#f8f9fa')

    # 设置总标题
    fig.suptitle('第二产业L2层级行业分布占比变化趋势\n(2010年、2020年、2024年对比)',
                fontsize=18, fontweight='bold', y=0.95)

    # 调整子图间距
    plt.tight_layout()
    plt.subplots_adjust(top=0.88, wspace=0.4)

    # 保存图表
    plt.savefig(output_file, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"图表已保存: {output_file}")

    # 同时保存PDF版本
    pdf_file = output_file.replace('.png', '.pdf')
    plt.savefig(pdf_file, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"PDF版本已保存: {pdf_file}")

    plt.close()

def create_top_industries_chart(data, output_file, top_n=15):
    """
    创建前N个行业的三个独立横向柱状图（水平排列）

    Args:
        data: 处理后的数据
        output_file: 输出文件路径
        top_n: 显示前N个行业
    """
    print(f"创建前{top_n}个行业的三个独立横向柱状图（水平排列）...")

    # 取前N个行业（基于2024年占比排序）
    top_data = data.head(top_n)

    # 设置图表尺寸
    fig_width = max(20, top_n * 1.2)  # 根据行业数量动态调整宽度
    fig_height = max(10, top_n * 0.6)  # 根据行业数量动态调整高度

    # 创建3个子图，水平排列
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(fig_width, fig_height), dpi=300)

    # 准备数据
    industries = top_data['行业名称'].tolist()
    year_2010_ratio = np.array(top_data['2010年占比(%)'].tolist())
    year_2020_ratio = np.array(top_data['2020年占比(%)'].tolist())
    year_2024_ratio = np.array(top_data['2024年占比(%)'].tolist())

    # 企业数量数据（检查列名是否存在）
    if '2010年企业数' in top_data.columns:
        year_2010_count = np.array(top_data['2010年企业数'].tolist())
        year_2020_count = np.array(top_data['2020年企业数'].tolist())
        year_2024_count = np.array(top_data['2024年企业数'].tolist())
    else:
        # 如果没有企业数量列，使用占比数据作为替代显示
        year_2010_count = year_2010_ratio
        year_2020_count = year_2020_ratio
        year_2024_count = year_2024_ratio

    # 设置柱状图位置
    y = np.arange(len(industries))
    height = 0.7

    # 使用统一的颜色方案
    bar_color = '#3498db'  # 统一使用蓝色

    # 设置X轴范围（所有子图使用相同范围）
    max_value = max(np.max(year_2010_ratio), np.max(year_2020_ratio), np.max(year_2024_ratio))
    xlim_max = max_value * 1.2

    # 创建Y轴标签（行业名称+占比）
    y_labels_2010 = [f"{name}({ratio:.1f}%)" for name, ratio in zip(industries, year_2010_ratio)]
    y_labels_2020 = [f"{name}({ratio:.1f}%)" for name, ratio in zip(industries, year_2020_ratio)]
    y_labels_2024 = [f"{name}({ratio:.1f}%)" for name, ratio in zip(industries, year_2024_ratio)]

    # 第一个子图：2010年数据
    ax1.barh(y, year_2010_ratio, height, color=bar_color, alpha=0.8, edgecolor='white', linewidth=0.5)
    ax1.set_title('2010年', fontsize=16, fontweight='bold', pad=20)
    ax1.set_xlim(0, xlim_max)
    ax1.grid(True, axis='x', alpha=0.3, linestyle='--', color='gray')
    ax1.set_xlabel('占比 (%)', fontsize=12, fontweight='bold')
    ax1.set_yticks(y)
    ax1.set_yticklabels(y_labels_2010, fontsize=10)

    # 添加数据标签
    for i, count in enumerate(year_2010_count):
        if year_2010_ratio[i] > 1.0:  # 只显示占比大于1%的值
            if '2010年企业数' in top_data.columns:
                ax1.text(year_2010_ratio[i] + xlim_max * 0.01, y[i], f'{count:,}家',
                        ha='left', va='center', fontsize=9, fontweight='bold')
            else:
                ax1.text(year_2010_ratio[i] + xlim_max * 0.01, y[i], f'{count:.1f}%',
                        ha='left', va='center', fontsize=9, fontweight='bold')

    # 第二个子图：2020年数据
    ax2.barh(y, year_2020_ratio, height, color=bar_color, alpha=0.8, edgecolor='white', linewidth=0.5)
    ax2.set_title('2020年', fontsize=16, fontweight='bold', pad=20)
    ax2.set_xlim(0, xlim_max)
    ax2.grid(True, axis='x', alpha=0.3, linestyle='--', color='gray')
    ax2.set_xlabel('占比 (%)', fontsize=12, fontweight='bold')
    ax2.set_yticks(y)
    ax2.set_yticklabels(y_labels_2020, fontsize=10)

    # 添加数据标签
    for i, count in enumerate(year_2020_count):
        if year_2020_ratio[i] > 1.0:
            if '2020年企业数' in top_data.columns:
                ax2.text(year_2020_ratio[i] + xlim_max * 0.01, y[i], f'{count:,}家',
                        ha='left', va='center', fontsize=9, fontweight='bold')
            else:
                ax2.text(year_2020_ratio[i] + xlim_max * 0.01, y[i], f'{count:.1f}%',
                        ha='left', va='center', fontsize=9, fontweight='bold')

    # 第三个子图：2024年数据
    ax3.barh(y, year_2024_ratio, height, color=bar_color, alpha=0.8, edgecolor='white', linewidth=0.5)
    ax3.set_title('2024年', fontsize=16, fontweight='bold', pad=20)
    ax3.set_xlim(0, xlim_max)
    ax3.grid(True, axis='x', alpha=0.3, linestyle='--', color='gray')
    ax3.set_xlabel('占比 (%)', fontsize=12, fontweight='bold')
    ax3.set_yticks(y)
    ax3.set_yticklabels(y_labels_2024, fontsize=10)

    # 添加数据标签
    for i, count in enumerate(year_2024_count):
        if year_2024_ratio[i] > 1.0:
            if '2024年企业数' in top_data.columns:
                ax3.text(year_2024_ratio[i] + xlim_max * 0.01, y[i], f'{count:,}家',
                        ha='left', va='center', fontsize=9, fontweight='bold')
            else:
                ax3.text(year_2024_ratio[i] + xlim_max * 0.01, y[i], f'{count:.1f}%',
                        ha='left', va='center', fontsize=9, fontweight='bold')

    # 反转Y轴，使最大的行业显示在顶部
    for ax in [ax1, ax2, ax3]:
        ax.invert_yaxis()
        ax.tick_params(axis='x', labelsize=10)
        ax.set_facecolor('#f8f9fa')

    # 设置总标题
    fig.suptitle(f'第二产业L2层级前{top_n}个行业占比变化趋势\n(2010年、2020年、2024年对比)',
                fontsize=16, fontweight='bold', y=0.95)

    # 调整子图间距
    plt.tight_layout()
    plt.subplots_adjust(top=0.88, wspace=0.4)

    # 保存图表
    plt.savefig(output_file, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"前{top_n}行业图表已保存: {output_file}")

    plt.close()

def main():
    """主函数"""
    # 输入文件
    excel_file = "sz_industry_analysis_by_levels_20250729.xlsx"
    sheet_name = "二产L2"

    # 输出文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file_all = f"secondary_industry_l2_all_chart_{timestamp}.png"
    output_file_top = f"secondary_industry_l2_top15_chart_{timestamp}.png"

    print("=" * 60)
    print("企业行业分布数据可视化")
    print("=" * 60)

    try:
        # 1. 设置中文字体
        setup_chinese_font()

        # 2. 检查输入文件
        if not os.path.exists(excel_file):
            raise FileNotFoundError(f"Excel文件不存在: {excel_file}")

        # 3. 加载数据
        df = load_data(excel_file, sheet_name)
        if df is None:
            raise ValueError("数据加载失败")

        # 4. 准备数据
        data = prepare_data(df)
        if data is None or len(data) == 0:
            raise ValueError("数据处理失败或无有效数据")

        # 5. 创建完整图表
        print("\n创建完整行业图表...")
        create_stacked_bar_chart(data, output_file_all)

        # 6. 创建前15个行业图表
        print("\n创建前15个行业图表...")
        create_top_industries_chart(data, output_file_top, top_n=15)

        print("=" * 60)
        print("可视化完成！")
        print(f"完整图表: {output_file_all}")
        print(f"前15行业图表: {output_file_top}")
        print(f"处理行业数量: {len(data)}")
        print("=" * 60)

        # 显示数据概览
        print("\n数据概览（前10个行业）:")
        print(data.head(10)[['行业名称', '2024年占比(%)']].to_string(index=False))

        # 显示增长趋势分析
        print("\n增长趋势分析（前5个行业）:")
        for i, row in data.head(5).iterrows():
            name = row['行业名称']
            ratio_2010 = row['2010年占比(%)']
            ratio_2020 = row['2020年占比(%)']
            ratio_2024 = row['2024年占比(%)']

            growth_2010_2020 = ratio_2020 - ratio_2010
            growth_2020_2024 = ratio_2024 - ratio_2020

            print(f"  {name}:")
            print(f"    2010-2020年变化: {growth_2010_2020:+.2f}%")
            print(f"    2020-2024年变化: {growth_2020_2024:+.2f}%")

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
