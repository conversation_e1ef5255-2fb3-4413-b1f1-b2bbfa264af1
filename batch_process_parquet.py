#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理Parquet文件脚本
功能：从指定目录读取所有parquet文件，提取指定字段，按100万条记录分批保存
"""

import pandas as pd
import os
import glob
from datetime import datetime
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_process.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_output_directory():
    """创建输出目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"processed_company_data_{timestamp}"
    Path(output_dir).mkdir(exist_ok=True)
    logger.info(f"创建输出目录: {output_dir}")
    return output_dir

def get_parquet_files(source_path):
    """获取源目录下的所有parquet文件"""
    pattern = os.path.join(source_path, "*.parquet")
    files = glob.glob(pattern)
    logger.info(f"找到 {len(files)} 个parquet文件")
    return files

def clean_timestamp_columns(df, timestamp_columns=['establish_date', 'cancel_date', 'revoke_date']):
    """
    清理时间戳列，处理无效的时间戳数据

    Args:
        df: DataFrame
        timestamp_columns: 需要处理的时间戳列名列表

    Returns:
        处理后的DataFrame
    """
    for col in timestamp_columns:
        if col in df.columns:
            try:
                # 如果列已经是datetime类型，检查是否有无效值
                if pd.api.types.is_datetime64_any_dtype(df[col]):
                    # 检查是否有超出范围的时间戳
                    invalid_mask = (df[col] < pd.Timestamp('1677-09-21')) | (df[col] > pd.Timestamp('2262-04-11'))
                    if invalid_mask.any():
                        invalid_count = invalid_mask.sum()
                        logger.warning(f"列 {col} 中发现 {invalid_count} 个超出范围的时间戳，将设置为NaT")
                        df.loc[invalid_mask, col] = pd.NaT
                else:
                    # 如果是object类型，尝试转换为datetime
                    if df[col].dtype == 'object':
                        # 尝试转换，无效值设为NaT
                        df[col] = pd.to_datetime(df[col], errors='coerce', utc=False)
                        invalid_count = df[col].isna().sum()
                        if invalid_count > 0:
                            logger.info(f"列 {col} 中有 {invalid_count} 个无效时间戳已转换为NaT")
            except Exception as e:
                logger.warning(f"处理时间戳列 {col} 时出错: {str(e)}，保持原始格式")

    return df

def process_parquet_files(source_path, batch_size=1000000):
    """
    批量处理parquet文件
    
    Args:
        source_path: 源文件路径
        batch_size: 每批处理的记录数，默认100万条
    """
    # 需要提取的字段
    required_columns = [
        'lc_company_id',
        'company_name',
        'company_status_clean',
        'industry_l1_code',
        'city_code',
        'establish_date',
        'cancel_date',
        'revoke_date'
    ]
    
    # 创建输出目录
    output_dir = create_output_directory()
    
    # 获取所有parquet文件
    parquet_files = get_parquet_files(source_path)
    
    if not parquet_files:
        logger.error(f"在路径 {source_path} 下未找到任何parquet文件")
        return
    
    # 初始化变量
    batch_data = []
    batch_count = 0
    total_processed = 0
    current_batch_size = 0
    
    logger.info(f"开始处理 {len(parquet_files)} 个文件...")
    
    try:
        for file_idx, file_path in enumerate(parquet_files, 1):
            logger.info(f"正在处理文件 {file_idx}/{len(parquet_files)}: {os.path.basename(file_path)}")
            
            try:
                # 直接读取整个文件，使用字符串类型读取时间戳字段避免转换错误
                try:
                    # 首先尝试正常读取
                    df = pd.read_parquet(file_path, columns=required_columns)
                except Exception as timestamp_error:
                    if "timestamp" in str(timestamp_error).lower() or "out of bounds" in str(timestamp_error).lower():
                        logger.warning(f"文件 {os.path.basename(file_path)} 存在时间戳转换问题，使用字符串格式读取: {str(timestamp_error)}")
                        # 使用pyarrow直接读取，避免pandas的时间戳转换
                        import pyarrow.parquet as pq
                        table = pq.read_table(file_path, columns=required_columns)
                        df = table.to_pandas(timestamp_as_object=True)
                    else:
                        raise timestamp_error

                # 检查字段是否存在
                missing_cols = [col for col in required_columns if col not in df.columns]
                if missing_cols:
                    logger.warning(f"文件 {os.path.basename(file_path)} 缺少字段: {missing_cols}")
                    # 为缺少的字段添加空值
                    for col in missing_cols:
                        df[col] = None

                # 只保留需要的字段，确保顺序一致
                df = df[required_columns]

                # 清理时间戳数据
                df = clean_timestamp_columns(df)

                # 添加城市代码筛选条件：只保留深圳市的数据 (city_code='440300')
                if 'city_code' in df.columns:
                    original_count = len(df)
                    df = df[df['city_code'] == '440300'].copy()
                    filtered_count = len(df)
                    logger.info(f"文件 {os.path.basename(file_path)} 筛选结果: 原始记录 {original_count} 条，筛选后 {filtered_count} 条")

                    # 如果筛选后没有数据，跳过这个文件
                    if len(df) == 0:
                        logger.info(f"文件 {os.path.basename(file_path)} 筛选后无数据，跳过处理")
                        continue
                else:
                    logger.warning(f"文件 {os.path.basename(file_path)} 缺少 city_code 字段，跳过筛选")

                # 如果当前文件的数据量很大，分批处理
                if len(df) > batch_size:
                    # 先保存当前累积的数据（如果有）
                    if batch_data and current_batch_size > 0:
                        current_batch_df = pd.concat(batch_data, ignore_index=True)
                        batch_count += 1
                        output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
                        current_batch_df.to_parquet(output_file, index=False)
                        total_processed += len(current_batch_df)
                        logger.info(f"保存批次 {batch_count}: {len(current_batch_df)} 条记录 -> {output_file}")

                        # 重置累积数据
                        batch_data = []
                        current_batch_size = 0

                    # 将大文件分批保存
                    start_idx = 0
                    while start_idx < len(df):
                        end_idx = min(start_idx + batch_size, len(df))
                        batch_chunk = df.iloc[start_idx:end_idx].copy()

                        batch_count += 1
                        output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
                        batch_chunk.to_parquet(output_file, index=False)
                        total_processed += len(batch_chunk)
                        logger.info(f"保存批次 {batch_count}: {len(batch_chunk)} 条记录 -> {output_file}")

                        start_idx = end_idx
                else:
                    # 文件不大，添加到累积数据中
                    batch_data.append(df)
                    current_batch_size += len(df)

                    # 检查是否达到批次大小
                    if current_batch_size >= batch_size:
                        # 合并当前批次数据
                        batch_df = pd.concat(batch_data, ignore_index=True)

                        # 如果超过批次大小，分割数据
                        if len(batch_df) > batch_size:
                            # 保存完整批次
                            save_batch = batch_df.iloc[:batch_size].copy()
                            remaining_data = batch_df.iloc[batch_size:].copy()

                            # 保存批次
                            batch_count += 1
                            output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
                            save_batch.to_parquet(output_file, index=False)
                            total_processed += len(save_batch)
                            logger.info(f"保存批次 {batch_count}: {len(save_batch)} 条记录 -> {output_file}")

                            # 重置批次数据，保留剩余数据
                            batch_data = [remaining_data] if len(remaining_data) > 0 else []
                            current_batch_size = len(remaining_data)
                        else:
                            # 保存完整批次
                            batch_count += 1
                            output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
                            batch_df.to_parquet(output_file, index=False)
                            total_processed += len(batch_df)
                            logger.info(f"保存批次 {batch_count}: {len(batch_df)} 条记录 -> {output_file}")

                            # 重置批次数据
                            batch_data = []
                            current_batch_size = 0
                            
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                continue
        
        # 处理最后一批数据（如果有剩余）
        if batch_data and current_batch_size > 0:
            final_batch = pd.concat(batch_data, ignore_index=True)
            batch_count += 1
            output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
            final_batch.to_parquet(output_file, index=False)
            total_processed += len(final_batch)
            logger.info(f"保存最终批次 {batch_count}: {len(final_batch)} 条记录 -> {output_file}")
        
        logger.info(f"处理完成！总共处理 {total_processed} 条记录，生成 {batch_count} 个批次文件")
        logger.info(f"输出目录: {output_dir}")
        
    except Exception as e:
        logger.error(f"批量处理过程中发生错误: {str(e)}")
        raise

def main():
    """主函数"""
    # 源数据路径
    source_path = r"F:\蕾奥工作\20.数据库转到本地\output\company_general_info_v3"
    
    # 检查源路径是否存在
    if not os.path.exists(source_path):
        logger.error(f"源路径不存在: {source_path}")
        return
    
    logger.info(f"开始批量处理parquet文件")
    logger.info(f"源路径: {source_path}")
    logger.info(f"批次大小: 1,000,000 条记录")
    logger.info(f"筛选条件: city_code='440300' (深圳市)")
    
    try:
        process_parquet_files(source_path)
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")

if __name__ == "__main__":
    main()
